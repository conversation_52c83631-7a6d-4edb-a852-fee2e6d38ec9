<template>
  <div class="review-upload-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-upload text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">上传审核</h2>
          <p class="text-muted mb-0">审核专利上传申请</p>
        </div>
      </div>

      <div class="card shadow-sm">
        <div class="card-body text-center py-5">
          <i class="bi bi-upload text-muted" style="font-size: 4rem;"></i>
          <h4 class="text-muted mt-3">上传审核功能</h4>
          <p class="text-muted">此功能正在开发中...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReviewUploadView'
}
</script>

<style scoped>
.review-upload-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}
</style>
