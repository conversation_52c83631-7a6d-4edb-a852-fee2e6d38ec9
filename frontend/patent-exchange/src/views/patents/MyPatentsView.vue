<template>
  <div class="my-patents-view">
    <div class="container-fluid py-4">
      <!-- <PERSON> Header -->
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-folder text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">我的专利</h2>
          <p class="text-muted mb-0">管理您的专利资产</p>
        </div>
      </div>

      <!-- Tabs -->
      <ul class="nav nav-tabs mb-4" id="patentTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="uploaded-tab"
            data-bs-toggle="tab"
            data-bs-target="#uploaded"
            type="button"
            role="tab"
          >
            <i class="bi bi-upload me-2"></i>
            已上传专利 <span class="badge bg-primary ms-1">{{ uploadedPatents.length }}</span>
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="purchased-tab"
            data-bs-toggle="tab"
            data-bs-target="#purchased"
            type="button"
            role="tab"
          >
            <i class="bi bi-cart-check me-2"></i>
            已购买专利 <span class="badge bg-success ms-1">{{ purchasedPatents.length }}</span>
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="sold-tab"
            data-bs-toggle="tab"
            data-bs-target="#sold"
            type="button"
            role="tab"
          >
            <i class="bi bi-cash-coin me-2"></i>
            已出售专利 <span class="badge bg-warning ms-1">{{ soldPatents.length }}</span>
          </button>
        </li>
      </ul>

      <!-- Tab Content -->
      <div class="tab-content" id="patentTabsContent">
        <!-- Uploaded Patents -->
        <div class="tab-pane fade show active" id="uploaded" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in uploadedPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="card-title text-truncate-2 mb-0">{{ patent.name }}</h6>
                    <span :class="getStatusBadgeClass(patent.status)" class="badge ms-2">
                      {{ getStatusText(patent.status) }}
                    </span>
                  </div>
                  
                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>
                  
                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>
                  
                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-primary">{{ patent.price }} ETH</div>
                        <small class="text-muted">转让价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-success">{{ formatDate(patent.uploadDate) }}</div>
                      <small class="text-muted">上传日期</small>
                    </div>
                  </div>
                  
                  <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm flex-fill">
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button 
                      v-if="patent.status === 'approved'"
                      class="btn btn-outline-success btn-sm flex-fill"
                    >
                      <i class="bi bi-pencil me-1"></i>
                      编辑
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="uploadedPatents.length === 0" class="text-center py-5">
            <i class="bi bi-upload text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无上传的专利</h5>
            <p class="text-muted">
              <router-link to="/patents/upload" class="text-decoration-none">
                点击这里上传您的第一个专利
              </router-link>
            </p>
          </div>
        </div>

        <!-- Purchased Patents -->
        <div class="tab-pane fade" id="purchased" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in purchasedPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h6 class="card-title text-truncate-2 mb-3">{{ patent.name }}</h6>
                  
                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>
                  
                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>
                  
                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-success">{{ patent.purchasePrice }} ETH</div>
                        <small class="text-muted">购买价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-info">{{ formatDate(patent.purchaseDate) }}</div>
                      <small class="text-muted">购买日期</small>
                    </div>
                  </div>
                  
                  <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm flex-fill">
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button class="btn btn-outline-success btn-sm flex-fill">
                      <i class="bi bi-download me-1"></i>
                      下载文档
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="purchasedPatents.length === 0" class="text-center py-5">
            <i class="bi bi-cart text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无购买的专利</h5>
            <p class="text-muted">
              <router-link to="/patents/search" class="text-decoration-none">
                去搜索专利进行购买
              </router-link>
            </p>
          </div>
        </div>

        <!-- Sold Patents -->
        <div class="tab-pane fade" id="sold" role="tabpanel">
          <div class="row g-4">
            <div
              v-for="patent in soldPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h6 class="card-title text-truncate-2 mb-3">{{ patent.name }}</h6>
                  
                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>
                  
                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>
                  
                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-warning">{{ patent.salePrice }} ETH</div>
                        <small class="text-muted">出售价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-secondary">{{ formatDate(patent.saleDate) }}</div>
                      <small class="text-muted">出售日期</small>
                    </div>
                  </div>
                  
                  <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm flex-fill">
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    <button class="btn btn-outline-info btn-sm flex-fill">
                      <i class="bi bi-receipt me-1"></i>
                      交易记录
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="soldPatents.length === 0" class="text-center py-5">
            <i class="bi bi-cash-coin text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无出售的专利</h5>
            <p class="text-muted">当您的专利被购买后，会在这里显示</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'MyPatentsView',
  setup() {
    const authStore = useAuthStore()
    
    const uploadedPatents = ref([])
    const purchasedPatents = ref([])
    const soldPatents = ref([])

    // Mock data
    const mockUploadedPatents = [
      {
        id: 1,
        name: '智能手机快速充电技术',
        number: 'CN202123456789.1',
        abstract: '本发明涉及一种智能手机快速充电技术，能够在30分钟内充电至80%...',
        price: 0.5,
        status: 'approved',
        uploadDate: '2023-01-15'
      }
    ]

    const mockPurchasedPatents = [
      {
        id: 2,
        name: '环保汽车发动机设计',
        number: 'CN202123456790.2',
        abstract: '环保型汽车发动机设计，减少尾气排放50%...',
        purchasePrice: 2.3,
        purchaseDate: '2023-03-20'
      }
    ]

    const mockSoldPatents = [
      {
        id: 3,
        name: '智能家居控制系统',
        number: 'CN202123456791.3',
        abstract: '智能家居控制系统，支持语音控制和远程操作...',
        salePrice: 1.8,
        saleDate: '2023-02-10'
      }
    ]

    const getStatusBadgeClass = (status) => {
      const classes = {
        pending: 'bg-warning',
        approved: 'bg-success',
        rejected: 'bg-danger'
      }
      return classes[status] || 'bg-secondary'
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return texts[status] || '未知状态'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    onMounted(() => {
      // Load user's patents from blockchain/API
      uploadedPatents.value = mockUploadedPatents
      purchasedPatents.value = mockPurchasedPatents
      soldPatents.value = mockSoldPatents
    })

    return {
      authStore,
      uploadedPatents,
      purchasedPatents,
      soldPatents,
      getStatusBadgeClass,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.my-patents-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-bottom: 2px solid #0d6efd;
  color: #0d6efd;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}
</style>
