<template>
  <div class="patent-search-view">
    <div class="container-fluid py-4">
      <!-- Page Header -->
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-search text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">专利搜索</h2>
          <p class="text-muted mb-0">搜索和浏览可用的专利</p>
        </div>
      </div>

      <!-- Search Filters -->
      <div class="card shadow-sm mb-4">
        <div class="card-body">
          <form @submit.prevent="searchPatents">
            <div class="row g-3">
              <div class="col-md-4">
                <label for="searchName" class="form-label">专利名称</label>
                <input
                  type="text"
                  class="form-control"
                  id="searchName"
                  v-model="searchFilters.name"
                  placeholder="输入专利名称关键词"
                >
              </div>
              
              <div class="col-md-4">
                <label for="searchNumber" class="form-label">专利号</label>
                <input
                  type="text"
                  class="form-control"
                  id="searchNumber"
                  v-model="searchFilters.number"
                  placeholder="输入专利号"
                >
              </div>
              
              <div class="col-md-4">
                <label for="searchCategory" class="form-label">专利类别</label>
                <select
                  class="form-select"
                  id="searchCategory"
                  v-model="searchFilters.category"
                >
                  <option value="">全部类别</option>
                  <option value="invention">发明专利</option>
                  <option value="utility">实用新型专利</option>
                  <option value="design">外观设计专利</option>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="priceRange" class="form-label">价格范围 (ETH)</label>
                <div class="row g-2">
                  <div class="col">
                    <input
                      type="number"
                      step="0.001"
                      class="form-control"
                      placeholder="最低价格"
                      v-model="searchFilters.minPrice"
                    >
                  </div>
                  <div class="col-auto d-flex align-items-center">
                    <span class="text-muted">-</span>
                  </div>
                  <div class="col">
                    <input
                      type="number"
                      step="0.001"
                      class="form-control"
                      placeholder="最高价格"
                      v-model="searchFilters.maxPrice"
                    >
                  </div>
                </div>
              </div>
              
              <div class="col-md-6 d-flex align-items-end">
                <div class="d-flex gap-2 w-100">
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="isSearching"
                  >
                    <span v-if="isSearching" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-search me-2"></i>
                    搜索
                  </button>
                  
                  <button
                    type="button"
                    class="btn btn-outline-secondary"
                    @click="clearFilters"
                  >
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    清除
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Search Results -->
      <div class="row">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
              搜索结果 
              <span class="badge bg-primary">{{ patents.length }}</span>
            </h5>
            
            <div class="d-flex gap-2">
              <select class="form-select form-select-sm" v-model="sortBy" style="width: auto;">
                <option value="name">按名称排序</option>
                <option value="price">按价格排序</option>
                <option value="date">按日期排序</option>
              </select>
              
              <button
                class="btn btn-outline-secondary btn-sm"
                @click="toggleSortOrder"
              >
                <i :class="sortOrder === 'asc' ? 'bi bi-sort-alpha-down' : 'bi bi-sort-alpha-up'"></i>
              </button>
            </div>
          </div>

          <!-- Patent Cards -->
          <div v-if="patents.length > 0" class="row g-4">
            <div
              v-for="patent in sortedPatents"
              :key="patent.id"
              class="col-lg-6 col-xl-4"
            >
              <div class="card h-100 shadow-sm patent-card">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h6 class="card-title text-truncate-2 mb-0">{{ patent.name }}</h6>
                    <span :class="getCategoryBadgeClass(patent.category)" class="badge ms-2">
                      {{ getCategoryText(patent.category) }}
                    </span>
                  </div>
                  
                  <p class="text-muted small mb-2">
                    <i class="bi bi-hash me-1"></i>
                    {{ patent.number }}
                  </p>
                  
                  <p class="card-text text-truncate-3 mb-3">{{ patent.abstract }}</p>
                  
                  <div class="row text-center mb-3">
                    <div class="col-6">
                      <div class="border-end">
                        <div class="fw-bold text-primary">{{ patent.price }} ETH</div>
                        <small class="text-muted">转让价格</small>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="fw-bold text-success">{{ patent.owner }}</div>
                      <small class="text-muted">专利权人</small>
                    </div>
                  </div>
                  
                  <div class="d-flex gap-2">
                    <button
                      class="btn btn-outline-primary btn-sm flex-fill"
                      @click="viewPatentDetails(patent)"
                    >
                      <i class="bi bi-eye me-1"></i>
                      查看详情
                    </button>
                    
                    <button
                      class="btn btn-primary btn-sm flex-fill"
                      @click="initiateTransaction(patent)"
                    >
                      <i class="bi bi-cart-plus me-1"></i>
                      购买
                    </button>
                  </div>
                </div>
                
                <div class="card-footer bg-transparent">
                  <small class="text-muted">
                    <i class="bi bi-calendar me-1"></i>
                    申请日期：{{ formatDate(patent.applicationDate) }}
                  </small>
                </div>
              </div>
            </div>
          </div>

          <!-- No Results -->
          <div v-else-if="!isSearching" class="text-center py-5">
            <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无搜索结果</h5>
            <p class="text-muted">请尝试调整搜索条件</p>
          </div>

          <!-- Loading -->
          <div v-if="isSearching" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">搜索中...</span>
            </div>
            <p class="text-muted mt-3">正在搜索专利...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'PatentSearchView',
  setup() {
    const authStore = useAuthStore()
    
    const searchFilters = reactive({
      name: '',
      number: '',
      category: '',
      minPrice: '',
      maxPrice: ''
    })
    
    const patents = ref([])
    const isSearching = ref(false)
    const sortBy = ref('name')
    const sortOrder = ref('asc')

    // Mock patent data
    const mockPatents = [
      {
        id: 1,
        name: '一种新型智能手机充电器',
        number: 'CN202123456789.1',
        category: 'utility',
        abstract: '本发明涉及一种新型智能手机充电器，具有快速充电、智能识别设备类型等功能...',
        price: 0.5,
        owner: '张三',
        applicationDate: '2023-01-15',
        status: 'approved'
      },
      {
        id: 2,
        name: '环保型汽车发动机设计',
        number: 'CN202123456790.2',
        category: 'invention',
        abstract: '本发明提供了一种环保型汽车发动机设计方案，能够有效减少尾气排放...',
        price: 2.3,
        owner: '李四',
        applicationDate: '2023-02-20',
        status: 'approved'
      },
      {
        id: 3,
        name: '智能家居控制面板外观设计',
        number: 'CN202123456791.3',
        category: 'design',
        abstract: '本外观设计涉及智能家居控制面板的外观，具有简洁美观的设计风格...',
        price: 0.8,
        owner: '王五',
        applicationDate: '2023-03-10',
        status: 'approved'
      }
    ]

    const sortedPatents = computed(() => {
      const sorted = [...patents.value].sort((a, b) => {
        let aValue, bValue
        
        switch (sortBy.value) {
          case 'price':
            aValue = parseFloat(a.price)
            bValue = parseFloat(b.price)
            break
          case 'date':
            aValue = new Date(a.applicationDate)
            bValue = new Date(b.applicationDate)
            break
          default:
            aValue = a.name.toLowerCase()
            bValue = b.name.toLowerCase()
        }
        
        if (sortOrder.value === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
      
      return sorted
    })

    const searchPatents = async () => {
      try {
        isSearching.value = true
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Filter mock data based on search criteria
        let filtered = mockPatents.filter(patent => {
          const nameMatch = !searchFilters.name || 
            patent.name.toLowerCase().includes(searchFilters.name.toLowerCase())
          
          const numberMatch = !searchFilters.number || 
            patent.number.includes(searchFilters.number)
          
          const categoryMatch = !searchFilters.category || 
            patent.category === searchFilters.category
          
          const priceMatch = (!searchFilters.minPrice || patent.price >= parseFloat(searchFilters.minPrice)) &&
            (!searchFilters.maxPrice || patent.price <= parseFloat(searchFilters.maxPrice))
          
          return nameMatch && numberMatch && categoryMatch && priceMatch
        })
        
        patents.value = filtered
        
      } catch (error) {
        console.error('Search failed:', error)
      } finally {
        isSearching.value = false
      }
    }

    const clearFilters = () => {
      Object.keys(searchFilters).forEach(key => {
        searchFilters[key] = ''
      })
      patents.value = []
    }

    const toggleSortOrder = () => {
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
    }

    const getCategoryBadgeClass = (category) => {
      const classes = {
        invention: 'bg-primary',
        utility: 'bg-success',
        design: 'bg-warning'
      }
      return classes[category] || 'bg-secondary'
    }

    const getCategoryText = (category) => {
      const texts = {
        invention: '发明专利',
        utility: '实用新型',
        design: '外观设计'
      }
      return texts[category] || '未知类型'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const viewPatentDetails = (patent) => {
      // Navigate to patent details page
      console.log('View patent details:', patent)
    }

    const initiateTransaction = (patent) => {
      // Navigate to transaction page
      console.log('Initiate transaction for patent:', patent)
    }

    onMounted(() => {
      // Load initial patents
      searchPatents()
    })

    return {
      authStore,
      searchFilters,
      patents,
      isSearching,
      sortBy,
      sortOrder,
      sortedPatents,
      searchPatents,
      clearFilters,
      toggleSortOrder,
      getCategoryBadgeClass,
      getCategoryText,
      formatDate,
      viewPatentDetails,
      initiateTransaction
    }
  }
}
</script>

<style scoped>
.patent-search-view {
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  padding: 0;
  margin: 0;
}

.patent-card {
  transition: transform 0.2s ease-in-out;
  border: none;
  border-radius: 0.75rem;
}

.patent-card:hover {
  transform: translateY(-2px);
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Ensure container takes full width */
.container-fluid {
  width: 100% !important;
  max-width: none !important;
}
</style>
