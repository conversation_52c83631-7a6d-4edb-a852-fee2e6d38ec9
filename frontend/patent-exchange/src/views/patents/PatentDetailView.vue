<template>
  <div class="patent-detail-view">
    <div class="container-fluid py-4">
      <!-- Back Button -->
      <div class="mb-3">
        <button class="btn btn-outline-secondary" @click="goBack">
          <i class="bi bi-arrow-left me-2"></i>
          返回
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载专利详情...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Patent Details -->
      <div v-else-if="patent" class="row">
        <!-- Main Patent Information -->
        <div class="col-lg-8">
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
              <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                  <i class="bi bi-file-earmark-text me-2"></i>
                  {{ patent.name }}
                </h4>
                <span class="badge bg-light text-dark">{{ patent.category }}</span>
              </div>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利号</label>
                  <div class="fw-bold">{{ patent.number }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">转让价格</label>
                  <div class="fw-bold text-success">¥{{ formatPrice(patent.price) }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">申请日期</label>
                  <div class="fw-bold">{{ formatDate(patent.applicationDate) }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利权结束日期</label>
                  <div class="fw-bold">{{ formatDate(patent.expiryDate) }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利权人</label>
                  <div class="fw-bold">{{ patent.ownerName }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">上传者地址</label>
                  <div>
                    <button
                      class="btn btn-link p-0 text-decoration-none"
                      @click="showUserDetails(patent.uploaderAddress)"
                    >
                      {{ formatAddress(patent.uploaderAddress) }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                </div>
                <div class="col-12">
                  <label class="form-label text-muted small">专利摘要</label>
                  <div class="border rounded p-3 bg-light">
                    {{ patent.abstract }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Patent Documents -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-info text-white">
              <h5 class="mb-0">
                <i class="bi bi-file-earmark-pdf me-2"></i>
                专利文档
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                    <div>
                      <h6 class="mb-1">专利文档</h6>
                      <small class="text-muted">PDF格式</small>
                    </div>
                    <div>
                      <button class="btn btn-outline-primary btn-sm me-2" @click="viewDocument('patent')">
                        <i class="bi bi-eye me-1"></i>
                        查看
                      </button>
                      <button class="btn btn-primary btn-sm" @click="downloadDocument('patent')">
                        <i class="bi bi-download me-1"></i>
                        下载
                      </button>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ patent.isProxySale ? '代理委托证明' : '专利权证明' }}</h6>
                      <small class="text-muted">PDF格式</small>
                    </div>
                    <div>
                      <button class="btn btn-outline-primary btn-sm me-2" @click="viewDocument('certificate')">
                        <i class="bi bi-eye me-1"></i>
                        查看
                      </button>
                      <button class="btn btn-primary btn-sm" @click="downloadDocument('certificate')">
                        <i class="bi bi-download me-1"></i>
                        下载
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Panel -->
        <div class="col-lg-4">
          <!-- Patent Status -->
          <div class="card shadow-sm mb-3">
            <div class="card-body text-center">
              <div class="mb-3">
                <span :class="getStatusBadgeClass(patent.status)" class="badge fs-6 px-3 py-2">
                  {{ getStatusText(patent.status) }}
                </span>
              </div>
              <h3 class="text-success mb-3">¥{{ formatPrice(patent.price) }}</h3>
              
              <!-- Action Buttons -->
              <div class="d-grid gap-2" v-if="canTrade">
                <button
                  class="btn btn-success btn-lg"
                  @click="initiateTransaction"
                  :disabled="isTransacting"
                >
                  <span v-if="isTransacting" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-cart-plus me-2"></i>
                  {{ isTransacting ? '处理中...' : '购买专利' }}
                </button>
                <button class="btn btn-outline-warning" @click="initiateRightsProtection">
                  <i class="bi bi-shield-exclamation me-2"></i>
                  专利维权
                </button>
              </div>
              
              <div v-else class="alert alert-warning" role="alert">
                <small>{{ getUnavailableReason(patent.status) }}</small>
              </div>
            </div>
          </div>

          <!-- Patent Statistics -->
          <div class="card shadow-sm mb-3">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-graph-up me-2"></i>
                浏览统计
              </h6>
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">浏览次数</span>
                <span class="fw-bold">{{ patent.viewCount }}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span class="text-muted">下载次数</span>
                <span class="fw-bold">{{ patent.downloadCount }}</span>
              </div>
            </div>
          </div>

          <!-- Upload Information -->
          <div class="card shadow-sm">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-info-circle me-2"></i>
                上传信息
              </h6>
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">上传时间</span>
                <span class="fw-bold">{{ formatDate(patent.uploadDate) }}</span>
              </div>
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">代理出售</span>
                <span class="fw-bold">{{ patent.isProxySale ? '是' : '否' }}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span class="text-muted">专利状态</span>
                <span :class="getStatusBadgeClass(patent.status)" class="badge">
                  {{ getStatusText(patent.status) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { patentService } from '@/services/patentService'
import { transactionService } from '@/services/transactionService'
import UserDetailModal from '@/components/UserDetailModal.vue'

export default {
  name: 'PatentDetailView',
  components: {
    UserDetailModal
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    const isLoading = ref(false)
    const isTransacting = ref(false)
    const error = ref(null)
    const successMessage = ref(null)
    const patent = ref(null)
    const selectedUserAddress = ref(null)

    const canTrade = computed(() => {
      return patent.value && patent.value.status === 'normal' && 
             patent.value.uploaderAddress !== authStore.account
    })

    const loadPatentDetails = async () => {
      try {
        isLoading.value = true
        error.value = null
        
        const patentId = route.params.id
        const details = await patentService.getPatentDetails(patentId)
        patent.value = details
        
      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    const initiateTransaction = async () => {
      try {
        isTransacting.value = true
        
        const transactionData = {
          patentId: patent.value.id,
          buyerAddress: authStore.account,
          sellerAddress: patent.value.uploaderAddress,
          price: patent.value.price
        }
        
        const result = await transactionService.initiateTransaction(transactionData)
        
        if (result.success) {
          successMessage.value = result.message
          // Update patent status
          patent.value.status = 'trading'
        }
        
      } catch (err) {
        error.value = err.message
      } finally {
        isTransacting.value = false
      }
    }

    const initiateRightsProtection = () => {
      router.push({
        name: 'patent-protection',
        query: { patentId: patent.value.id }
      })
    }

    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new bootstrap.Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    const viewDocument = (type) => {
      console.log(`查看文档: ${type}`)
      // TODO: Implement document viewer
    }

    const downloadDocument = async (type) => {
      try {
        await patentService.downloadPatentDocument(patent.value.id, type)
      } catch (err) {
        error.value = err.message
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    // Utility functions
    const formatPrice = (price) => {
      return new Intl.NumberFormat('zh-CN').format(price)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const getStatusText = (status) => {
      const statusMap = {
        'normal': '正常',
        'trading': '交易中',
        'under_review': '审核中',
        'under_protection': '维权中'
      }
      return statusMap[status] || '未知'
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        'normal': 'bg-success',
        'trading': 'bg-warning',
        'under_review': 'bg-info',
        'under_protection': 'bg-danger'
      }
      return classMap[status] || 'bg-secondary'
    }

    const getUnavailableReason = (status) => {
      const reasonMap = {
        'trading': '该专利正在交易中',
        'under_review': '该专利正在审核中',
        'under_protection': '该专利正在维权中'
      }
      return reasonMap[status] || '该专利暂不可交易'
    }

    onMounted(() => {
      loadPatentDetails()
    })

    return {
      isLoading,
      isTransacting,
      error,
      successMessage,
      patent,
      selectedUserAddress,
      canTrade,
      initiateTransaction,
      initiateRightsProtection,
      showUserDetails,
      viewDocument,
      downloadDocument,
      goBack,
      formatPrice,
      formatDate,
      formatAddress,
      getStatusText,
      getStatusBadgeClass,
      getUnavailableReason
    }
  }
}
</script>

<style scoped>
.patent-detail-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.badge {
  border-radius: 6px;
}
</style>
