<template>
  <div class="admin-users-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-people text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">用户管理</h2>
          <p class="text-muted mb-0">管理系统用户</p>
        </div>
      </div>

      <div class="card shadow-sm">
        <div class="card-body text-center py-5">
          <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
          <h4 class="text-muted mt-3">用户管理功能</h4>
          <p class="text-muted">此功能正在开发中...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminUsersView'
}
</script>

<style scoped>
.admin-users-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}
</style>
