<template>
  <div class="app-layout">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container-fluid">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-shield-check me-2"></i>
          专利交易系统
        </router-link>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <!-- User Navigation -->
            <li v-if="authStore.isUser" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                专利管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/patents/upload">上传专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/search">搜索专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/my-patents">我的专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/trading">专利交易</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/protection">专利维权</router-link></li>
              </ul>
            </li>

            <!-- Reviewer Navigation -->
            <li v-if="authStore.isReviewer" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                审核管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/review/pending">待审核列表</router-link></li>
                <li><router-link class="dropdown-item" to="/review/upload">上传审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/trading">交易审核</router-link></li>
                <li><router-link class="dropdown-item" to="/review/protection">维权审核</router-link></li>
                <li><hr class="dropdown-divider"></li>
                <li><router-link class="dropdown-item" to="/patents/upload">上传专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/search">搜索专利</router-link></li>
                <li><router-link class="dropdown-item" to="/patents/protection">专利维权</router-link></li>
              </ul>
            </li>

            <!-- Admin Navigation -->
            <li v-if="authStore.isAdmin" class="nav-item dropdown" :class="{ 'nav-loading': authStore.isRoleLoading }">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
              >
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm me-2"></span>
                系统管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/admin/users">用户管理</router-link></li>
                <li><router-link class="dropdown-item" to="/admin/transactions">交易记录</router-link></li>
                <li><router-link class="dropdown-item" to="/admin/statistics">系统统计</router-link></li>
              </ul>
            </li>
          </ul>

          <!-- Wallet Connection -->
          <div class="navbar-nav">
            <div v-if="!authStore.isConnected" class="nav-item">
              <button
                class="btn btn-outline-light"
                @click="connectWallet"
                :disabled="authStore.isAnyLoading"
              >
                <span v-if="authStore.isAnyLoading" class="spinner-border spinner-border-sm me-2"></span>
                连接钱包
              </button>
            </div>

            <div v-else class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle text-light"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
                :class="{ 'nav-loading': authStore.isRoleLoading }"
              >
                <i class="bi bi-wallet2 me-2"></i>
                {{ authStore.shortAddress }}
                <span v-if="authStore.isRoleLoading" class="spinner-border spinner-border-sm ms-2"></span>
                <span v-else class="badge bg-success ms-2">{{ roleText }}</span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end">
                <li><h6 class="dropdown-header">账户信息</h6></li>
                <li><span class="dropdown-item-text">地址: {{ authStore.account }}</span></li>
                <li><span class="dropdown-item-text">角色: {{ roleText }}</span></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <router-link class="dropdown-item" to="/profile/personal-info">
                    <i class="bi bi-person me-2"></i>
                    个人信息
                  </router-link>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <button class="dropdown-item" @click="disconnectWallet">
                    <i class="bi bi-box-arrow-right me-2"></i>
                    断开连接
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Error Alert -->
    <div v-if="authStore.error" class="alert alert-danger alert-dismissible fade show m-3" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i>
      {{ authStore.error }}
      <button type="button" class="btn-close" @click="authStore.error = null"></button>
    </div>

    <!-- Role Change Notification -->
    <div
      v-if="authStore.roleChangeNotification"
      class="alert alert-dismissible fade show m-3"
      :class="getNotificationClass(authStore.roleChangeNotification.type)"
      role="alert"
    >
      <i :class="getNotificationIcon(authStore.roleChangeNotification.type)" class="me-2"></i>
      {{ authStore.roleChangeNotification.message }}
      <button type="button" class="btn-close" @click="clearNotification"></button>
    </div>

    <!-- Main Content -->
    <main class="flex-grow-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-6">
            <h6>基于区块链的专利交易系统</h6>
            <p class="text-muted small">
              使用 Vue.js + Bootstrap + MetaMask + IPFS + Ganache 构建
            </p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="text-muted small">
              © 2024 专利交易系统. 保留所有权利.
            </p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/auth'
import { computed } from 'vue'

export default {
  name: 'AppLayout',
  setup() {
    const authStore = useAuthStore()

    const roleText = computed(() => {
      switch (authStore.userRole) {
        case 'admin': return '管理员'
        case 'reviewer': return '审核方'
        case 'user': return '用户'
        default: return '未知'
      }
    })

    const connectWallet = async () => {
      await authStore.connectWallet()
    }

    const disconnectWallet = () => {
      authStore.disconnectWallet()
    }

    const clearNotification = () => {
      authStore.clearRoleNotification()
    }

    const getNotificationClass = (type) => {
      switch (type) {
        case 'success': return 'alert-success'
        case 'warning': return 'alert-warning'
        case 'info': return 'alert-info'
        case 'error': return 'alert-danger'
        default: return 'alert-info'
      }
    }

    const getNotificationIcon = (type) => {
      switch (type) {
        case 'success': return 'bi bi-check-circle'
        case 'warning': return 'bi bi-exclamation-triangle'
        case 'info': return 'bi bi-info-circle'
        case 'error': return 'bi bi-x-circle'
        default: return 'bi bi-info-circle'
      }
    }

    return {
      authStore,
      roleText,
      connectWallet,
      disconnectWallet,
      clearNotification,
      getNotificationClass,
      getNotificationIcon
    }
  }
}
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.navbar {
  width: 100%;
}

.navbar-brand {
  font-weight: bold;
}

.dropdown-item-text {
  font-size: 0.875rem;
  color: #6c757d;
}

main {
  flex: 1;
  width: 100%;
  padding: 0;
  margin: 0;
}

footer {
  width: 100%;
}

/* Ensure no margins on alerts */
.alert {
  margin-left: 0;
  margin-right: 0;
  border-radius: 0;
}

/* Loading states */
.nav-loading {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.nav-loading .nav-link {
  pointer-events: none;
}

/* Role transition animations */
.navbar-nav .nav-item {
  transition: all 0.3s ease;
}

/* Notification animations */
.alert {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
