// Transaction service for managing patent transactions and rights protection
export const transactionService = {
  // Get pending transactions for review (reviewer view)
  async getPendingTransactions() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'tx_001',
          patentId: '1',
          patentName: '一种新型智能手机充电技术',
          patentNumber: 'CN202410001234.5',
          buyerAddress: '0x2345678901234567890123456789012345678901',
          buyerName: '李四',
          sellerAddress: '0x1234567890123456789012345678901234567890',
          sellerName: '张三',
          price: '50000',
          submitDate: '2024-01-20 10:30:00',
          status: 'pending',
          type: 'purchase'
        },
        {
          id: 'tx_002',
          patentId: '2',
          patentName: '智能家居控制系统',
          patentNumber: 'CN202410001235.6',
          buyerAddress: '0x3456789012345678901234567890123456789012',
          buyerName: '王五',
          sellerAddress: '0x2345678901234567890123456789012345678901',
          sellerName: '李四',
          price: '80000',
          submitDate: '2024-01-21 14:20:00',
          status: 'pending',
          type: 'purchase'
        }
      ]
    } catch (error) {
      console.error('获取待审核交易失败:', error)
      throw new Error('获取待审核交易失败')
    }
  },

  // Get user's transactions
  async getUserTransactions(userAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'tx_003',
          patentId: '3',
          patentName: '人工智能图像识别算法',
          patentNumber: 'CN202410001236.7',
          buyerAddress: userAddress,
          buyerName: '当前用户',
          sellerAddress: '0x4567890123456789012345678901234567890123',
          sellerName: '赵六',
          price: '120000',
          submitDate: '2024-01-18 16:45:00',
          approvalDate: '2024-01-19 09:30:00',
          status: 'approved',
          type: 'purchase'
        }
      ]
    } catch (error) {
      console.error('获取用户交易记录失败:', error)
      throw new Error('获取用户交易记录失败')
    }
  },

  // Initiate patent transaction
  async initiateTransaction(transactionData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('发起专利交易:', transactionData)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      return {
        success: true,
        transactionId: 'tx_' + Date.now(),
        message: '交易申请已提交，等待审核'
      }
    } catch (error) {
      console.error('发起交易失败:', error)
      throw new Error('发起交易失败')
    }
  },

  // Approve transaction (reviewer action)
  async approveTransaction(transactionId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准交易:', transactionId, '审核员:', reviewerAddress)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        message: '交易已批准'
      }
    } catch (error) {
      console.error('批准交易失败:', error)
      throw new Error('批准交易失败')
    }
  },

  // Reject transaction (reviewer action)
  async rejectTransaction(transactionId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝交易:', transactionId, '审核员:', reviewerAddress, '原因:', reason)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        message: '交易已拒绝'
      }
    } catch (error) {
      console.error('拒绝交易失败:', error)
      throw new Error('拒绝交易失败')
    }
  },

  // Get pending rights protection cases
  async getPendingProtectionCases() {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      return [
        {
          id: 'prot_001',
          patentId: '4',
          patentName: '区块链数据存储优化方案',
          patentNumber: 'CN202410001237.8',
          claimantAddress: '0x5678901234567890123456789012345678901234',
          claimantName: '孙七',
          currentOwnerAddress: '0x6789012345678901234567890123456789012345',
          currentOwnerName: '周八',
          description: '我是该专利的真正发明人，有充分的证据证明专利权归属。',
          evidenceHash: 'QmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ',
          submitDate: '2024-01-22 11:15:00',
          status: 'pending'
        }
      ]
    } catch (error) {
      console.error('获取待审核维权案例失败:', error)
      throw new Error('获取待审核维权案例失败')
    }
  },

  // Initiate rights protection
  async initiateRightsProtection(protectionData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('发起专利维权:', protectionData)
      
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      return {
        success: true,
        caseId: 'prot_' + Date.now(),
        message: '维权申请已提交，等待审核'
      }
    } catch (error) {
      console.error('发起维权失败:', error)
      throw new Error('发起维权失败')
    }
  },

  // Approve rights protection (reviewer action)
  async approveRightsProtection(caseId, reviewerAddress) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('批准维权:', caseId, '审核员:', reviewerAddress)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        message: '维权申请已批准，专利权已转移'
      }
    } catch (error) {
      console.error('批准维权失败:', error)
      throw new Error('批准维权失败')
    }
  },

  // Reject rights protection (reviewer action)
  async rejectRightsProtection(caseId, reviewerAddress, reason) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('拒绝维权:', caseId, '审核员:', reviewerAddress, '原因:', reason)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        message: '维权申请已拒绝'
      }
    } catch (error) {
      console.error('拒绝维权失败:', error)
      throw new Error('拒绝维权失败')
    }
  }
}
