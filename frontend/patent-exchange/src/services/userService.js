// User service for managing user information and profiles
export const userService = {
  // Get user profile information by blockchain address
  async getUserProfile(address) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data
      return {
        address: address,
        name: '张三',
        phone: '13800138000',
        idNumber: '110101199001011234',
        registrationDate: '2024-01-15',
        lastLoginDate: '2024-01-20'
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw new Error('获取用户资料失败')
    }
  },

  // Update user profile information
  async updateUserProfile(address, profileData) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      console.log('更新用户资料:', address, profileData)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        message: '用户资料更新成功'
      }
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw new Error('更新用户资料失败')
    }
  },

  // Get user details for display in modals (when clicking blockchain addresses)
  async getUserDetails(address) {
    try {
      // TODO: Replace with actual API call to backend/smart contract
      // For now, return mock data based on address
      const mockUsers = {
        '0x1234567890123456789012345678901234567890': {
          name: '李四',
          phone: '13900139000',
          idNumber: '110101199002021234',
          address: '0x1234567890123456789012345678901234567890'
        },
        '0x0987654321098765432109876543210987654321': {
          name: '王五',
          phone: '13700137000',
          idNumber: '110101199003031234',
          address: '0x0987654321098765432109876543210987654321'
        }
      }

      return mockUsers[address] || {
        name: '未知用户',
        phone: '未知',
        idNumber: '未知',
        address: address
      }
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw new Error('获取用户详情失败')
    }
  },

  // Validate user profile data
  validateProfile(profileData) {
    const errors = []

    if (!profileData.name || profileData.name.trim().length < 2) {
      errors.push('姓名至少需要2个字符')
    }

    if (!profileData.phone || !/^1[3-9]\d{9}$/.test(profileData.phone)) {
      errors.push('请输入有效的手机号码')
    }

    if (!profileData.idNumber || !/^\d{17}[\dXx]$/.test(profileData.idNumber)) {
      errors.push('请输入有效的身份证号码')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
}
